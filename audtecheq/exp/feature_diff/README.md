# Audio-Based At-Risk Classification Pipeline

This module implements a complete pipeline for classifying participants into at-risk groups based on audio recordings using pretrained embeddings and machine learning.

## Overview

The pipeline uses the following approach:
1. **Pretrained Embeddings**: Extract features using Wav2Vec2 model
2. **Per-Participant Aggregation**: Average embeddings across all audio files per participant
3. **Voice Activity Detection**: Optional VAD to remove silence
4. **Classification**: Train XGBoost/Random Forest/Logistic Regression models

This approach avoids the need for speaker diarization by statistically learning to ignore adult speech through aggregation.

## Architecture

### Core Modules

- **`config.py`**: Configuration management with dataclasses
- **`data_loader.py`**: Participant metadata and audio file management
- **`audio_processor.py`**: Audio processing, VAD, and embedding extraction
- **`classifier.py`**: Machine learning models and evaluation
- **`main_pipeline.py`**: Main orchestration pipeline

### Key Classes

1. **ConfigManager**: Manages all configuration settings
2. **ParticipantDataLoader**: Loads and processes participant metadata
3. **AudioFileManager**: Discovers and organizes audio files
4. **AudioEmbeddingExtractor**: Extracts embeddings using Wav2Vec2
5. **ParticipantEmbeddingAggregator**: Aggregates embeddings per participant
6. **AtRiskClassifier**: Main classification model
7. **AudioAtRiskPipeline**: Complete end-to-end pipeline

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# For GPU support (optional but recommended)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Usage

### Basic Usage

```python
from pathlib import Path
from main_pipeline import AudioAtRiskPipeline

# Initialize pipeline
pipeline = AudioAtRiskPipeline(base_dir=Path("path/to/data"))

# Run complete pipeline
results = pipeline.run_full_pipeline()
```

### Step-by-Step Usage

```python
# 1. Load participant data
participant_labels = pipeline.load_participant_data()

# 2. Discover audio files
audio_files = pipeline.discover_audio_files()

# 3. Extract embeddings
embeddings = pipeline.extract_embeddings(audio_files)

# 4. Train and evaluate
results = pipeline.train_and_evaluate(embeddings, participant_labels)
```

### Configuration

Create a `pipeline_config.json` file:

```json
{
  "audio": {
    "sample_rate": 16000,
    "max_duration": null,
    "apply_vad": true,
    "vad_threshold": 0.5,
    "embedding_model": "facebook/wav2vec2-base-960h"
  },
  "model": {
    "classifier_type": "xgboost",
    "test_size": 0.2,
    "random_state": 42,
    "cross_validation_folds": 5
  }
}
```

## Data Structure

Expected directory structure:
```
data_directory/
├── participants.tsv          # Participant metadata
├── participants.json         # Field definitions
├── audio/                    # Audio files directory
│   ├── CHI001_recording1.wav
│   ├── CHI001_recording2.wav
│   ├── ATL002_recording1.wav
│   └── ...
└── output/                   # Generated outputs
    ├── participants_at_risk_groups.csv
    ├── participant_embeddings.npz
    └── trained_model.joblib
```

## Features

### Audio Processing
- **Pretrained Embeddings**: Uses Wav2Vec2 for robust feature extraction
- **Voice Activity Detection**: Energy-based VAD to remove silence
- **Flexible Audio Loading**: Supports multiple audio formats
- **GPU Acceleration**: Automatic GPU usage when available

### Machine Learning
- **Multiple Algorithms**: XGBoost, Random Forest, Logistic Regression
- **Cross-Validation**: Stratified k-fold validation
- **Model Comparison**: Automatic comparison of different algorithms
- **Feature Scaling**: Automatic standardization of features

### Data Management
- **JSON-Driven Configuration**: All mappings read from participants.json
- **Robust File Discovery**: Automatic audio file discovery
- **Caching**: Embeddings cached to avoid recomputation
- **Error Handling**: Comprehensive error handling and logging

## Output Files

1. **`participants_at_risk_groups.csv`**: Processed participant data with at-risk groups
2. **`participant_embeddings.npz`**: Cached embeddings for all participants
3. **`trained_model.joblib`**: Saved trained model with preprocessing objects

## Model Performance

The pipeline provides comprehensive evaluation metrics:
- Test set accuracy
- Cross-validation scores with confidence intervals
- Per-class precision, recall, and F1-scores
- Confusion matrix
- Feature importance (when available)

## Customization

### Adding New Models

```python
# In classifier.py, extend the _initialize_model method
def _initialize_model(self):
    if model_type == "your_model":
        from your_module import YourModel
        self.model = YourModel(**your_params)
```

### Custom Audio Processing

```python
# Extend AudioEmbeddingExtractor
class CustomEmbeddingExtractor(AudioEmbeddingExtractor):
    def extract_embedding(self, audio):
        # Your custom embedding extraction
        return custom_embedding
```

### Custom Aggregation

```python
# In ParticipantEmbeddingAggregator
def aggregate_participant_embeddings(self, audio_files, method="custom"):
    if method == "custom":
        # Your custom aggregation logic
        return custom_aggregated_embedding
```

## Troubleshooting

### Common Issues

1. **No audio files found**: Check audio directory path and filename patterns
2. **CUDA out of memory**: Reduce batch size or use CPU
3. **Missing dependencies**: Install all requirements from requirements.txt
4. **Model loading errors**: Ensure transformers library is properly installed

### Performance Tips

1. **Use GPU**: Install CUDA-enabled PyTorch for faster embedding extraction
2. **Cache embeddings**: Set `force_recompute_embeddings=False` to reuse cached embeddings
3. **Optimize VAD**: Tune VAD threshold based on your audio characteristics
4. **Parallel processing**: The pipeline can be extended for parallel audio processing

## Contributing

To extend the pipeline:
1. Follow the modular class-based structure
2. Add comprehensive logging
3. Include error handling
4. Update configuration options
5. Add unit tests

## License

This module is part of the AudioTechEquity project.
