"""
Audio processing module for embedding extraction and voice activity detection.
"""

import numpy as np
import torch
import torchaudio
import librosa
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union
import logging
import warnings

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)

try:
    from transformers import Wav2Vec2Processor, Wav2Vec2Model
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers library not available. Install with: pip install transformers")

from config import ConfigManager


class VoiceActivityDetector:
    """Voice Activity Detection using energy-based approach."""

    def __init__(self, threshold: float = 0.5, frame_length: int = 2048, hop_length: int = 512):
        """
        Initialize VAD.

        Args:
            threshold: Energy threshold for voice activity
            frame_length: Frame length for analysis
            hop_length: Hop length for analysis
        """
        self.threshold = threshold
        self.frame_length = frame_length
        self.hop_length = hop_length

        self.logger = logging.getLogger(__name__)

    def detect_voice_activity(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Detect voice activity in audio signal.

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Boolean array indicating voice activity
        """
        # Compute short-time energy
        energy = librosa.feature.rms(
            y=audio,
            frame_length=self.frame_length,
            hop_length=self.hop_length
        )[0]

        # Normalize energy
        energy_normalized = (energy - np.min(energy)) / (np.max(energy) - np.min(energy) + 1e-8)

        # Apply threshold
        voice_activity = energy_normalized > self.threshold

        # Convert frame-level decisions to sample-level
        voice_samples = np.repeat(voice_activity, self.hop_length)

        # Ensure same length as original audio
        if len(voice_samples) > len(audio):
            voice_samples = voice_samples[:len(audio)]
        elif len(voice_samples) < len(audio):
            voice_samples = np.pad(voice_samples, (0, len(audio) - len(voice_samples)))

        return voice_samples

    def apply_vad(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Apply VAD to remove silence from audio.

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Audio with silence removed
        """
        voice_activity = self.detect_voice_activity(audio, sample_rate)

        if np.sum(voice_activity) == 0:
            self.logger.warning("No voice activity detected, returning original audio")
            return audio

        return audio[voice_activity]


class AudioEmbeddingExtractor:
    """Extracts embeddings from audio using pretrained models."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the embedding extractor.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.model = None
        self.processor = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Initialize VAD if enabled
        if self.config.audio_config.apply_vad:
            self.vad = VoiceActivityDetector(threshold=self.config.audio_config.vad_threshold)
        else:
            self.vad = None

        self.logger = logging.getLogger(__name__)
        self._load_model()

    def _load_model(self):
        """Load the pretrained embedding model."""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for embedding extraction")

        try:
            model_name = self.config.audio_config.embedding_model
            self.processor = Wav2Vec2Processor.from_pretrained(model_name)
            self.model = Wav2Vec2Model.from_pretrained(model_name)
            self.model.to(self.device)
            self.model.eval()

            self.logger.info(f"Loaded model: {model_name} on {self.device}")

        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            raise

    def load_audio(self, audio_path: Path) -> Tuple[np.ndarray, int]:
        """
        Load audio file.

        Args:
            audio_path: Path to audio file

        Returns:
            Tuple of (audio_data, sample_rate)
        """
        try:
            # Load audio using librosa for consistent preprocessing
            audio, sr = librosa.load(
                audio_path,
                sr=self.config.audio_config.sample_rate,
                duration=self.config.audio_config.max_duration
            )

            return audio, sr

        except Exception as e:
            self.logger.error(f"Error loading audio {audio_path}: {e}")
            raise

    def preprocess_audio(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Preprocess audio (apply VAD, normalization, etc.).

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Preprocessed audio
        """
        # Apply VAD if enabled
        if self.vad is not None:
            audio = self.vad.apply_vad(audio, sample_rate)

        # Normalize audio
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))

        return audio

    def extract_embedding(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract embedding from audio using the pretrained model.

        Args:
            audio: Preprocessed audio signal

        Returns:
            Embedding vector
        """
        try:
            # Process audio for model input
            inputs = self.processor(
                audio,
                sampling_rate=self.config.audio_config.sample_rate,
                return_tensors="pt",
                padding=True
            )

            # Move to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Extract features
            with torch.no_grad():
                outputs = self.model(**inputs)

                # Get the last hidden state and average over time dimension
                last_hidden_state = outputs.last_hidden_state  # Shape: (batch, time, features)
                embedding = torch.mean(last_hidden_state, dim=1)  # Average over time

                # Convert to numpy
                embedding = embedding.cpu().numpy().squeeze()

            return embedding

        except Exception as e:
            self.logger.error(f"Error extracting embedding: {e}")
            raise

    def process_audio_file(self, audio_path: Path) -> Optional[np.ndarray]:
        """
        Process a single audio file to extract embedding.

        Args:
            audio_path: Path to audio file

        Returns:
            Embedding vector or None if processing failed
        """
        try:
            # Load audio
            audio, sample_rate = self.load_audio(audio_path)

            # Check if audio is too short
            if len(audio) < self.config.audio_config.sample_rate * 0.1:  # Less than 0.1 seconds
                self.logger.warning(f"Audio too short: {audio_path}")
                return None

            # Preprocess
            audio = self.preprocess_audio(audio, sample_rate)

            # Check if audio remains after preprocessing
            if len(audio) == 0:
                self.logger.warning(f"No audio remaining after preprocessing: {audio_path}")
                return None

            # Extract embedding
            embedding = self.extract_embedding(audio)

            return embedding

        except Exception as e:
            self.logger.error(f"Error processing {audio_path}: {e}")
            return None


class ParticipantEmbeddingAggregator:
    """Aggregates embeddings across multiple audio files per participant."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the aggregator.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.extractor = AudioEmbeddingExtractor(config_manager)
        self.logger = logging.getLogger(__name__)

    def aggregate_participant_embeddings(
        self,
        audio_files: List[Path],
        aggregation_method: str = "mean"
    ) -> Optional[np.ndarray]:
        """
        Aggregate embeddings from multiple audio files for a participant.

        Args:
            audio_files: List of audio file paths for the participant
            aggregation_method: Method for aggregation ("mean", "median", "max")

        Returns:
            Aggregated embedding vector or None if no valid embeddings
        """
        embeddings = []

        for audio_file in audio_files:
            embedding = self.extractor.process_audio_file(audio_file)
            if embedding is not None:
                embeddings.append(embedding)

        if not embeddings:
            self.logger.warning(f"No valid embeddings extracted from {len(audio_files)} files")
            return None

        embeddings = np.array(embeddings)

        # Aggregate embeddings
        if aggregation_method == "mean":
            aggregated = np.mean(embeddings, axis=0)
        elif aggregation_method == "median":
            aggregated = np.median(embeddings, axis=0)
        elif aggregation_method == "max":
            aggregated = np.max(embeddings, axis=0)
        else:
            raise ValueError(f"Unknown aggregation method: {aggregation_method}")

        self.logger.info(f"Aggregated {len(embeddings)} embeddings using {aggregation_method}")

        return aggregated

    def process_all_participants(
        self,
        participant_audio_files: Dict[str, List[Path]]
    ) -> Dict[str, np.ndarray]:
        """
        Process all participants to extract aggregated embeddings.

        Args:
            participant_audio_files: Dictionary mapping participant IDs to audio file lists

        Returns:
            Dictionary mapping participant IDs to aggregated embeddings
        """
        participant_embeddings = {}

        total_participants = len(participant_audio_files)

        for i, (participant_id, audio_files) in enumerate(participant_audio_files.items(), 1):
            self.logger.info(f"Processing participant {participant_id} ({i}/{total_participants})")

            aggregated_embedding = self.aggregate_participant_embeddings(audio_files)

            if aggregated_embedding is not None:
                participant_embeddings[participant_id] = aggregated_embedding
            else:
                self.logger.warning(f"Failed to extract embedding for participant {participant_id}")

        self.logger.info(f"Successfully processed {len(participant_embeddings)} participants")

        return participant_embeddings

    def save_embeddings(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        output_path: Optional[Path] = None
    ):
        """
        Save participant embeddings to file.

        Args:
            participant_embeddings: Dictionary of participant embeddings
            output_path: Optional custom output path
        """
        if output_path is None:
            output_path = self.config.path_config.output_dir / "participant_embeddings.npz"

        # Convert to format suitable for saving
        participant_ids = list(participant_embeddings.keys())
        embeddings_array = np.array(list(participant_embeddings.values()))

        np.savez(
            output_path,
            participant_ids=participant_ids,
            embeddings=embeddings_array
        )

        self.logger.info(f"Saved embeddings for {len(participant_ids)} participants to {output_path}")

    def load_embeddings(self, input_path: Path) -> Dict[str, np.ndarray]:
        """
        Load participant embeddings from file.

        Args:
            input_path: Path to embeddings file

        Returns:
            Dictionary mapping participant IDs to embeddings
        """
        data = np.load(input_path)
        participant_ids = data['participant_ids']
        embeddings = data['embeddings']

        participant_embeddings = {
            pid: emb for pid, emb in zip(participant_ids, embeddings)
        }

        self.logger.info(f"Loaded embeddings for {len(participant_embeddings)} participants")

        return participant_embeddings
