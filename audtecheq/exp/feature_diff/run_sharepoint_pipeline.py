#!/usr/bin/env python3
"""
Example script to run the SharePoint pipeline with the provided credentials.
This script demonstrates how to use the SharePoint integration for the SPROUT dataset.
"""

import logging
from pathlib import Path
from sharepoint_pipeline import SharePointAudioPipeline
from sharepoint_connector import SharePointConfig


def main():
    """Run the SharePoint pipeline with the provided credentials."""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("="*60)
    print("SHAREPOINT AUDIO AT-RISK CLASSIFICATION PIPELINE")
    print("="*60)
    
    # SharePoint configuration with provided credentials
    sharepoint_config = SharePointConfig(
        sharepoint_url="https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/",
        username="<EMAIL>",
        password="Megalophiya*092426",
        base_path="Shared Documents/Datasets/ds-SPROUT"
    )
    
    # Initialize pipeline
    print("Initializing SharePoint pipeline...")
    try:
        pipeline = SharePointAudioPipeline(
            base_dir=Path(__file__).parent,
            sharepoint_config=sharepoint_config,
            config_file=Path(__file__).parent / "pipeline_config.json"
        )
        print("✓ Pipeline initialized successfully")
    except Exception as e:
        print(f"✗ Pipeline initialization failed: {e}")
        return
    
    # Run the complete pipeline
    print("\nStarting pipeline execution...")
    try:
        results = pipeline.run_sharepoint_pipeline(
            max_participants=10,  # Start with a small number for testing
            force_redownload_audio=False,  # Use cached files if available
            force_recompute_embeddings=False,  # Use cached embeddings if available
            compare_models=True  # Compare different ML models
        )
        
        print("\n" + "="*60)
        print("PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        # Print summary results
        if results:
            cache_stats = results.get('cache_statistics', {})
            print(f"Participants processed: {cache_stats.get('total_participants', 0)}")
            print(f"Audio files downloaded: {cache_stats.get('total_files', 0)}")
            print(f"Cache size: {cache_stats.get('total_size_mb', 0)} MB")
            
            if 'training_results' in results:
                training = results['training_results']
                print(f"Test accuracy: {training.get('test_accuracy', 0):.3f}")
                print(f"CV accuracy: {training.get('cv_mean', 0):.3f} ± {training.get('cv_std', 0):.3f}")
        
    except Exception as e:
        print(f"\n✗ Pipeline execution failed: {e}")
        print("This might be due to:")
        print("1. Missing dependencies (install with: pip install -r requirements.txt)")
        print("2. SharePoint connectivity issues")
        print("3. Missing audio files for participants")
        print("4. Insufficient permissions")


def test_connection_only():
    """Test SharePoint connection without running the full pipeline."""
    
    print("Testing SharePoint connection...")
    
    # Import test function
    from test_sharepoint import test_sharepoint_connection, test_participant_discovery
    
    sharepoint_config = SharePointConfig(
        sharepoint_url="https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/",
        username="<EMAIL>",
        password="Megalophiya*092426"
    )
    
    # Test connection
    connector = test_sharepoint_connection(sharepoint_config)
    if connector:
        # Test participant discovery
        participants = test_participant_discovery(connector)
        print(f"\nConnection test successful! Found {len(participants)} participants.")
        return True
    else:
        print("\nConnection test failed!")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test-connection":
        # Just test the connection
        success = test_connection_only()
        sys.exit(0 if success else 1)
    else:
        # Run the full pipeline
        main()
