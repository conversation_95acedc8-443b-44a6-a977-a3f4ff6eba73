"""
SharePoint connector module for downloading audio files from SharePoint.
Integrates with the audio processing pipeline to fetch data from SharePoint.
"""

import os
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import tempfile
import shutil
from urllib.parse import urljoin

try:
    from office365.runtime.auth.authentication_context import AuthenticationContext
    from office365.sharepoint.client_context import ClientContext
    from office365.sharepoint.files.file import File
    OFFICE365_AVAILABLE = True
except ImportError:
    OFFICE365_AVAILABLE = False
    print("Warning: Office365-REST-Python-Client not available. Install with: pip install Office365-REST-Python-Client")

from config import ConfigManager


class SharePointConfig:
    """Configuration for SharePoint connection."""
    
    def __init__(
        self,
        sharepoint_url: str,
        username: str,
        password: str,
        base_path: str = "Shared Documents/Datasets/ds-SPROUT"
    ):
        """
        Initialize SharePoint configuration.
        
        Args:
            sharepoint_url: SharePoint site URL
            username: SharePoint username
            password: SharePoint password
            base_path: Base path to the datasets
        """
        self.sharepoint_url = sharepoint_url
        self.username = username
        self.password = password
        self.base_path = base_path


class SharePointConnector:
    """Handles connection and file operations with SharePoint."""
    
    def __init__(self, sharepoint_config: SharePointConfig):
        """
        Initialize SharePoint connector.
        
        Args:
            sharepoint_config: SharePoint configuration
        """
        if not OFFICE365_AVAILABLE:
            raise ImportError("Office365-REST-Python-Client is required for SharePoint integration")
        
        self.config = sharepoint_config
        self.ctx = None
        self.logger = logging.getLogger(__name__)
        
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with SharePoint."""
        try:
            auth_ctx = AuthenticationContext(self.config.sharepoint_url)
            
            if auth_ctx.acquire_token_for_user(self.config.username, self.config.password):
                self.ctx = ClientContext(self.config.sharepoint_url, auth_ctx)
                self.logger.info("Successfully authenticated with SharePoint")
            else:
                raise Exception("Failed to authenticate with SharePoint")
                
        except Exception as e:
            self.logger.error(f"SharePoint authentication failed: {e}")
            raise
    
    def list_participant_directories(self) -> List[str]:
        """
        List all participant directories in SharePoint.
        
        Returns:
            List of participant directory names
        """
        try:
            # Get the folder
            folder = self.ctx.web.get_folder_by_server_relative_url(self.config.base_path)
            self.ctx.load(folder)
            self.ctx.execute_query()
            
            # List subdirectories (city codes)
            participant_dirs = []
            folders = folder.folders
            self.ctx.load(folders)
            self.ctx.execute_query()
            
            for city_folder in folders:
                city_name = city_folder.properties['Name']
                if city_name.startswith('ds-'):
                    # List participant subdirectories
                    city_path = f"{self.config.base_path}/{city_name}"
                    city_folder_obj = self.ctx.web.get_folder_by_server_relative_url(city_path)
                    sub_folders = city_folder_obj.folders
                    self.ctx.load(sub_folders)
                    self.ctx.execute_query()
                    
                    for sub_folder in sub_folders:
                        sub_name = sub_folder.properties['Name']
                        if sub_name.startswith('sub-'):
                            participant_id = sub_name.replace('sub-', '')
                            participant_dirs.append(participant_id)
            
            self.logger.info(f"Found {len(participant_dirs)} participant directories")
            return participant_dirs
            
        except Exception as e:
            self.logger.error(f"Error listing participant directories: {e}")
            return []
    
    def get_participant_audio_files(self, participant_id: str, city_code: str = None) -> List[str]:
        """
        Get list of audio files for a specific participant.
        
        Args:
            participant_id: Participant ID (e.g., CHI001)
            city_code: Optional city code, will be inferred if not provided
            
        Returns:
            List of audio file paths in SharePoint
        """
        try:
            # Infer city code from participant ID if not provided
            if city_code is None:
                city_code = participant_id[:3]  # First 3 characters (CHI, ATL, etc.)
            
            # Construct path: ds-<citycode>/sub-<participantid>/beh/
            participant_path = f"{self.config.base_path}/ds-{city_code}/sub-{participant_id}/beh"
            
            # Get the beh folder
            try:
                beh_folder = self.ctx.web.get_folder_by_server_relative_url(participant_path)
                files = beh_folder.files
                self.ctx.load(files)
                self.ctx.execute_query()
                
                # Filter for audio files
                audio_extensions = ['.wav', '.mp3', '.flac', '.m4a', '.aac']
                audio_files = []
                
                for file in files:
                    file_name = file.properties['Name']
                    if any(file_name.lower().endswith(ext) for ext in audio_extensions):
                        audio_files.append(f"{participant_path}/{file_name}")
                
                self.logger.info(f"Found {len(audio_files)} audio files for {participant_id}")
                return audio_files
                
            except Exception as e:
                self.logger.warning(f"No audio files found for {participant_id}: {e}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting audio files for {participant_id}: {e}")
            return []
    
    def download_file(self, sharepoint_path: str, local_path: Path) -> bool:
        """
        Download a file from SharePoint to local path.
        
        Args:
            sharepoint_path: Path to file in SharePoint
            local_path: Local path to save the file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure local directory exists
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Download file
            with open(local_path, 'wb') as local_file:
                file = self.ctx.web.get_file_by_server_relative_url(sharepoint_path)
                file.download(local_file)
                self.ctx.execute_query()
            
            self.logger.debug(f"Downloaded {sharepoint_path} to {local_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error downloading {sharepoint_path}: {e}")
            return False


class SharePointAudioManager:
    """Manages audio file downloading and caching from SharePoint."""
    
    def __init__(
        self, 
        config_manager: ConfigManager,
        sharepoint_config: SharePointConfig,
        cache_dir: Optional[Path] = None
    ):
        """
        Initialize SharePoint audio manager.
        
        Args:
            config_manager: Configuration manager
            sharepoint_config: SharePoint configuration
            cache_dir: Optional cache directory for downloaded files
        """
        self.config = config_manager
        self.sharepoint_config = sharepoint_config
        self.connector = SharePointConnector(sharepoint_config)
        
        # Set up cache directory
        if cache_dir is None:
            self.cache_dir = self.config.path_config.output_dir / "sharepoint_cache"
        else:
            self.cache_dir = cache_dir
        
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
    
    def get_participants_from_csv(self, csv_path: Optional[Path] = None) -> List[str]:
        """
        Get list of participants from the at-risk groups CSV.
        
        Args:
            csv_path: Optional path to CSV file
            
        Returns:
            List of participant IDs
        """
        if csv_path is None:
            csv_path = self.config.path_config.output_dir / "participants_at_risk_groups.csv"
        
        try:
            df = pd.read_csv(csv_path)
            participants = df['participant_id'].unique().tolist()
            
            # Filter out participants with missing data if desired
            # You can modify this logic based on your needs
            valid_participants = []
            for participant in participants:
                participant_row = df[df['participant_id'] == participant].iloc[0]
                if participant_row['at_risk_code'] != 'N/A':  # Exclude missing data
                    valid_participants.append(participant)
            
            self.logger.info(f"Found {len(valid_participants)} participants with valid labels")
            return valid_participants
            
        except Exception as e:
            self.logger.error(f"Error reading participants CSV: {e}")
            return []
    
    def download_participant_audio(
        self, 
        participant_id: str, 
        force_redownload: bool = False
    ) -> List[Path]:
        """
        Download audio files for a specific participant.
        
        Args:
            participant_id: Participant ID
            force_redownload: Whether to force redownload existing files
            
        Returns:
            List of local paths to downloaded audio files
        """
        # Create participant cache directory
        participant_cache = self.cache_dir / participant_id
        participant_cache.mkdir(exist_ok=True)
        
        # Check if files already exist
        if not force_redownload:
            existing_files = list(participant_cache.glob("*.wav")) + \
                           list(participant_cache.glob("*.mp3")) + \
                           list(participant_cache.glob("*.flac"))
            
            if existing_files:
                self.logger.info(f"Using cached files for {participant_id}")
                return existing_files
        
        # Get audio files from SharePoint
        sharepoint_files = self.connector.get_participant_audio_files(participant_id)
        
        if not sharepoint_files:
            self.logger.warning(f"No audio files found for {participant_id}")
            return []
        
        # Download files
        downloaded_files = []
        for sharepoint_path in sharepoint_files:
            file_name = Path(sharepoint_path).name
            local_path = participant_cache / file_name
            
            if self.connector.download_file(sharepoint_path, local_path):
                downloaded_files.append(local_path)
        
        self.logger.info(f"Downloaded {len(downloaded_files)} files for {participant_id}")
        return downloaded_files
    
    def download_all_participant_audio(
        self, 
        participant_ids: Optional[List[str]] = None,
        force_redownload: bool = False,
        max_participants: Optional[int] = None
    ) -> Dict[str, List[Path]]:
        """
        Download audio files for all participants.
        
        Args:
            participant_ids: Optional list of specific participants to download
            force_redownload: Whether to force redownload existing files
            max_participants: Optional limit on number of participants to process
            
        Returns:
            Dictionary mapping participant IDs to lists of local audio file paths
        """
        if participant_ids is None:
            participant_ids = self.get_participants_from_csv()
        
        if max_participants:
            participant_ids = participant_ids[:max_participants]
        
        participant_audio_files = {}
        total_participants = len(participant_ids)
        
        self.logger.info(f"Starting download for {total_participants} participants")
        
        for i, participant_id in enumerate(participant_ids, 1):
            self.logger.info(f"Processing participant {participant_id} ({i}/{total_participants})")
            
            try:
                audio_files = self.download_participant_audio(
                    participant_id, 
                    force_redownload=force_redownload
                )
                
                if audio_files:
                    participant_audio_files[participant_id] = audio_files
                else:
                    self.logger.warning(f"No audio files downloaded for {participant_id}")
                    
            except Exception as e:
                self.logger.error(f"Error processing {participant_id}: {e}")
                continue
        
        self.logger.info(f"Successfully downloaded audio for {len(participant_audio_files)} participants")
        return participant_audio_files
    
    def get_cache_statistics(self) -> Dict[str, int]:
        """
        Get statistics about cached files.
        
        Returns:
            Dictionary with cache statistics
        """
        stats = {
            'total_participants': 0,
            'total_files': 0,
            'total_size_mb': 0
        }
        
        if not self.cache_dir.exists():
            return stats
        
        for participant_dir in self.cache_dir.iterdir():
            if participant_dir.is_dir():
                stats['total_participants'] += 1
                
                for audio_file in participant_dir.glob("*"):
                    if audio_file.is_file():
                        stats['total_files'] += 1
                        stats['total_size_mb'] += audio_file.stat().st_size / (1024 * 1024)
        
        stats['total_size_mb'] = round(stats['total_size_mb'], 2)
        return stats
    
    def clear_cache(self, participant_id: Optional[str] = None):
        """
        Clear cached files.
        
        Args:
            participant_id: Optional specific participant to clear, or None for all
        """
        if participant_id:
            participant_cache = self.cache_dir / participant_id
            if participant_cache.exists():
                shutil.rmtree(participant_cache)
                self.logger.info(f"Cleared cache for {participant_id}")
        else:
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info("Cleared entire cache")


def create_sharepoint_config_from_args(
    sharepoint_url: str,
    username: str,
    password: str
) -> SharePointConfig:
    """
    Create SharePoint configuration from command line arguments.
    
    Args:
        sharepoint_url: SharePoint site URL
        username: SharePoint username
        password: SharePoint password
        
    Returns:
        SharePoint configuration object
    """
    return SharePointConfig(
        sharepoint_url=sharepoint_url,
        username=username,
        password=password
    )
