#!/usr/bin/env python3
"""
SharePoint-enabled pipeline for audio-based at-risk classification.
Downloads audio files from SharePoint and runs the complete classification pipeline.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import pandas as pd

# Import our modules
from config import ConfigManager
from data_loader import ParticipantDataLoader
from sharepoint_connector import SharePointConfig, SharePointAudioManager, create_sharepoint_config_from_args
from audio_processor import ParticipantEmbeddingAggregator
from classifier import AtRiskClassifier, ModelEvaluator, PredictionPipeline
from main_pipeline import AudioAtRiskPipeline
from checkpoint_manager import CheckpointManager


class SharePointAudioPipeline(AudioAtRiskPipeline):
    """Extended pipeline that works with SharePoint data."""

    def __init__(
        self,
        base_dir: Path,
        sharepoint_config: SharePointConfig,
        config_file: Optional[Path] = None,
        cache_dir: Optional[Path] = None,
        checkpoint_dir: Optional[Path] = None
    ):
        """
        Initialize SharePoint-enabled pipeline.

        Args:
            base_dir: Base directory containing metadata files
            sharepoint_config: SharePoint configuration
            config_file: Optional configuration file path
            cache_dir: Optional cache directory for downloaded files
            checkpoint_dir: Optional checkpoint directory
        """
        # Initialize base pipeline
        super().__init__(base_dir, config_file)

        # Initialize checkpoint manager
        self.checkpoint_manager = CheckpointManager(
            self.config_manager,
            checkpoint_dir
        )

        # Initialize SharePoint components
        self.sharepoint_audio_manager = SharePointAudioManager(
            self.config_manager,
            sharepoint_config,
            cache_dir
        )

        self.logger.info("SharePoint pipeline with checkpointing initialized successfully")

    def download_audio_files(
        self,
        participant_ids: Optional[List[str]] = None,
        force_redownload: bool = False,
        max_participants: Optional[int] = None
    ) -> Dict[str, List[Path]]:
        """
        Download audio files from SharePoint.

        Args:
            participant_ids: Optional list of specific participants to download
            force_redownload: Whether to force redownload existing files
            max_participants: Optional limit on number of participants to process

        Returns:
            Dictionary mapping participant IDs to audio file lists
        """
        self.logger.info("Downloading audio files from SharePoint...")

        # Get participants from CSV if not specified
        if participant_ids is None:
            participant_ids = self.get_valid_participants()

        # Download files
        participant_audio_files = self.sharepoint_audio_manager.download_all_participant_audio(
            participant_ids=participant_ids,
            force_redownload=force_redownload,
            max_participants=max_participants
        )

        # Log statistics
        stats = self.sharepoint_audio_manager.get_cache_statistics()
        self.logger.info(f"Cache statistics: {stats}")

        return participant_audio_files

    def get_valid_participants(self, exclude_missing_labels: bool = True) -> List[str]:
        """
        Get list of valid participants from the CSV file.

        Args:
            exclude_missing_labels: Whether to exclude participants with missing labels

        Returns:
            List of participant IDs
        """
        csv_path = self.config_manager.path_config.output_dir / "participants_at_risk_groups.csv"

        if not csv_path.exists():
            # Create the CSV first
            self.logger.info("Creating participants at-risk groups CSV...")
            self.load_participant_data()

        try:
            df = pd.read_csv(csv_path)

            if exclude_missing_labels:
                # Exclude participants with missing data or 'N/A' labels
                valid_df = df[
                    (df['at_risk_code'] != 'N/A') &
                    (df['at_risk_group'] != 'Missing data')
                ]
            else:
                valid_df = df

            participants = valid_df['participant_id'].unique().tolist()

            self.logger.info(f"Found {len(participants)} valid participants")
            return participants

        except Exception as e:
            self.logger.error(f"Error reading participants CSV: {e}")
            return []

    def run_sharepoint_pipeline(
        self,
        participant_ids: Optional[List[str]] = None,
        max_participants: Optional[int] = None,
        force_redownload_audio: bool = False,
        force_recompute_embeddings: bool = False,
        force_recompute_all: bool = False,
        compare_models: bool = True,
        resume_from_checkpoint: bool = True
    ) -> Dict[str, Any]:
        """
        Run the complete SharePoint-enabled pipeline with checkpointing.

        Args:
            participant_ids: Optional list of specific participants to process
            max_participants: Optional limit on number of participants
            force_redownload_audio: Whether to force redownload of audio files
            force_recompute_embeddings: Whether to force recomputation of embeddings
            force_recompute_all: Whether to force recomputation of all steps
            compare_models: Whether to compare different model types
            resume_from_checkpoint: Whether to resume from existing checkpoints

        Returns:
            Dictionary with all pipeline results
        """
        self.logger.info("Starting SharePoint audio at-risk classification pipeline with checkpointing...")

        # Print initial checkpoint status
        if resume_from_checkpoint:
            self.checkpoint_manager.print_checkpoint_status()

        try:
            # Step 1: Load participant metadata
            participant_labels = self._run_step_with_checkpoint(
                step_name="load_metadata",
                step_func=self._load_participant_data_with_checkpoint,
                force_recompute=force_recompute_all,
                resume_from_checkpoint=resume_from_checkpoint
            )

            # Step 2: Download audio files from SharePoint
            participant_audio_files = self._run_step_with_checkpoint(
                step_name="discover_audio",
                step_func=lambda: self._download_audio_files_with_checkpoint(
                    participant_ids, max_participants, force_redownload_audio or force_recompute_all
                ),
                force_recompute=force_redownload_audio or force_recompute_all,
                resume_from_checkpoint=resume_from_checkpoint
            )

            if not participant_audio_files:
                self.logger.error("No audio files downloaded. Cannot proceed.")
                return {}

            # Step 3: Extract embeddings
            participant_embeddings = self._run_step_with_checkpoint(
                step_name="extract_embeddings",
                step_func=lambda: self._extract_embeddings_with_checkpoint(
                    participant_audio_files, force_recompute_embeddings or force_recompute_all
                ),
                force_recompute=force_recompute_embeddings or force_recompute_all,
                resume_from_checkpoint=resume_from_checkpoint
            )

            if not participant_embeddings:
                self.logger.error("No embeddings extracted. Cannot proceed.")
                return {}

            # Step 4: Train and evaluate
            training_results = self._run_step_with_checkpoint(
                step_name="train_model",
                step_func=lambda: self._train_and_evaluate_with_checkpoint(
                    participant_embeddings, participant_labels
                ),
                force_recompute=force_recompute_all,
                resume_from_checkpoint=resume_from_checkpoint
            )

            # Step 5: Compare models (optional)
            comparison_results = {}
            if compare_models:
                self.logger.info("Step 5: Comparing different models...")
                comparison_results = self.compare_models(
                    participant_embeddings, participant_labels
                )

            # Compile final results
            final_results = {
                'training_results': training_results,
                'comparison_results': comparison_results,
                'num_participants_with_labels': len(participant_labels),
                'num_participants_with_audio': len(participant_audio_files),
                'num_participants_with_embeddings': len(participant_embeddings),
                'cache_statistics': self.sharepoint_audio_manager.get_cache_statistics(),
                'checkpoint_summary': self.checkpoint_manager.get_checkpoint_summary()
            }

            self.logger.info("SharePoint pipeline completed successfully!")
            self._print_sharepoint_results(final_results)

            return final_results

        except Exception as e:
            self.logger.error(f"SharePoint pipeline failed: {e}")
            self.logger.info("Pipeline state saved. You can resume from the last successful checkpoint.")
            raise

    def _run_step_with_checkpoint(self, step_name: str, step_func, force_recompute: bool = False, resume_from_checkpoint: bool = True):
        """Run a pipeline step with checkpoint support."""
        if resume_from_checkpoint and not self.checkpoint_manager.should_recompute_step(step_name, force_recompute):
            self.logger.info(f"Step '{step_name}' already completed, loading from checkpoint...")
            return self._load_step_from_checkpoint(step_name)
        else:
            self.logger.info(f"Running step '{step_name}'...")
            result = step_func()
            return result

    def _load_step_from_checkpoint(self, step_name: str):
        """Load step results from checkpoint."""
        if step_name == "load_metadata":
            return self.checkpoint_manager.load_labels()
        elif step_name == "discover_audio":
            return self.checkpoint_manager.load_audio_files_mapping()
        elif step_name == "extract_embeddings":
            return self.checkpoint_manager.load_embeddings()
        elif step_name == "train_model":
            model_and_results = self.checkpoint_manager.load_model_and_results()
            return model_and_results[1] if model_and_results else None
        else:
            return None

    def _load_participant_data_with_checkpoint(self):
        """Load participant data and save to checkpoint."""
        participant_labels = self.load_participant_data()
        self.checkpoint_manager.save_labels(participant_labels)
        return participant_labels

    def _download_audio_files_with_checkpoint(self, participant_ids, max_participants, force_redownload):
        """Download audio files and save to checkpoint."""
        participant_audio_files = self.download_audio_files(
            participant_ids=participant_ids,
            force_redownload=force_redownload,
            max_participants=max_participants
        )
        self.checkpoint_manager.save_audio_files_mapping(participant_audio_files)
        return participant_audio_files

    def _extract_embeddings_with_checkpoint(self, participant_audio_files, force_recompute):
        """Extract embeddings and save to checkpoint."""
        participant_embeddings = self.extract_embeddings(
            participant_audio_files,
            force_recompute=force_recompute
        )
        self.checkpoint_manager.save_embeddings(participant_embeddings)
        return participant_embeddings

    def _train_and_evaluate_with_checkpoint(self, participant_embeddings, participant_labels):
        """Train model and save to checkpoint."""
        training_results = self.train_and_evaluate(participant_embeddings, participant_labels)

        # Save model data (this would need to be extracted from the classifier)
        model_data = {
            'training_completed': True,
            'timestamp': datetime.now().isoformat()
        }

        self.checkpoint_manager.save_model_and_results(model_data, training_results)
        return training_results

    def _print_sharepoint_results(self, results: Dict[str, Any]):
        """Print SharePoint pipeline results."""
        print("\n" + "="*60)
        print("SHAREPOINT PIPELINE RESULTS")
        print("="*60)

        # Cache statistics
        cache_stats = results.get('cache_statistics', {})
        print(f"Downloaded audio for: {cache_stats.get('total_participants', 0)} participants")
        print(f"Total audio files: {cache_stats.get('total_files', 0)}")
        print(f"Total cache size: {cache_stats.get('total_size_mb', 0)} MB")

        # Checkpoint information
        checkpoint_summary = results.get('checkpoint_summary', {})
        if checkpoint_summary:
            print(f"\nCheckpoint directory: {checkpoint_summary.get('checkpoint_dir', 'N/A')}")
            completed_steps = checkpoint_summary.get('state', {}).get('completed_steps', [])
            print(f"Completed steps: {len(completed_steps)}")

        # Training results
        if 'training_results' in results:
            self._print_results(results)


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(
        description="SharePoint Audio At-Risk Classification Pipeline"
    )

    # SharePoint connection arguments
    parser.add_argument(
        "--sharepoint-url",
        required=True,
        help="SharePoint site URL"
    )
    parser.add_argument(
        "--sharepoint-username",
        required=True,
        help="SharePoint username"
    )
    parser.add_argument(
        "--sharepoint-password",
        required=True,
        help="SharePoint password"
    )

    # Pipeline arguments
    parser.add_argument(
        "--base-dir",
        type=Path,
        default=Path(__file__).parent,
        help="Base directory containing metadata files"
    )
    parser.add_argument(
        "--config-file",
        type=Path,
        help="Optional configuration file"
    )
    parser.add_argument(
        "--cache-dir",
        type=Path,
        help="Optional cache directory for downloaded files"
    )
    parser.add_argument(
        "--max-participants",
        type=int,
        help="Maximum number of participants to process"
    )
    parser.add_argument(
        "--participant-ids",
        nargs="+",
        help="Specific participant IDs to process"
    )
    parser.add_argument(
        "--force-redownload-audio",
        action="store_true",
        help="Force redownload of audio files"
    )
    parser.add_argument(
        "--force-recompute-embeddings",
        action="store_true",
        help="Force recomputation of embeddings"
    )
    parser.add_argument(
        "--no-model-comparison",
        action="store_true",
        help="Skip model comparison"
    )
    parser.add_argument(
        "--force-recompute-all",
        action="store_true",
        help="Force recomputation of all steps"
    )
    parser.add_argument(
        "--no-resume",
        action="store_true",
        help="Don't resume from checkpoints, start fresh"
    )
    parser.add_argument(
        "--checkpoint-dir",
        type=Path,
        help="Custom checkpoint directory"
    )
    parser.add_argument(
        "--clear-checkpoints",
        choices=["all", "metadata", "audio", "embeddings", "model"],
        help="Clear specific checkpoints before running"
    )
    parser.add_argument(
        "--show-checkpoints",
        action="store_true",
        help="Show checkpoint status and exit"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    args = parser.parse_args()

    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # Create SharePoint configuration
        sharepoint_config = create_sharepoint_config_from_args(
            args.sharepoint_url,
            args.sharepoint_username,
            args.sharepoint_password
        )

        # Initialize pipeline
        pipeline = SharePointAudioPipeline(
            base_dir=args.base_dir,
            sharepoint_config=sharepoint_config,
            config_file=args.config_file,
            cache_dir=args.cache_dir,
            checkpoint_dir=args.checkpoint_dir
        )

        # Handle checkpoint operations
        if args.show_checkpoints:
            pipeline.checkpoint_manager.print_checkpoint_status()
            return

        if args.clear_checkpoints:
            if args.clear_checkpoints == "all":
                pipeline.checkpoint_manager.clear_checkpoints()
            else:
                step_mapping = {
                    "metadata": "load_metadata",
                    "audio": "discover_audio",
                    "embeddings": "extract_embeddings",
                    "model": "train_model"
                }
                step_name = step_mapping.get(args.clear_checkpoints)
                if step_name:
                    pipeline.checkpoint_manager.clear_checkpoints(step_name)
            print(f"Cleared checkpoints: {args.clear_checkpoints}")

        # Run pipeline
        results = pipeline.run_sharepoint_pipeline(
            participant_ids=args.participant_ids,
            max_participants=args.max_participants,
            force_redownload_audio=args.force_redownload_audio,
            force_recompute_embeddings=args.force_recompute_embeddings,
            force_recompute_all=args.force_recompute_all,
            compare_models=not args.no_model_comparison,
            resume_from_checkpoint=not args.no_resume
        )

        print("\nPipeline completed successfully!")
        return results

    except KeyboardInterrupt:
        print("\nPipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nPipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    results = main()
