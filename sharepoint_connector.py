#!/usr/bin/env python3
"""
SharePoint Connector

This module provides a connector for SharePoint operations, including authentication,
file operations, and directory management.
"""

import os
import tempfile
import time
import re
from typing import Optional, List, Tuple
from pathlib import Path
from urllib.parse import urlparse, quote, unquote
from office365.runtime.auth.user_credential import UserCredential
from office365.sharepoint.client_context import ClientContext
from office365.sharepoint.files.file import File


class SharePointConnector:
    """Connector for SharePoint operations."""
    
    def __init__(self, site_url: str, username: str, password: str):
        """
        Initialize the SharePoint connector.
        
        Args:
            site_url: SharePoint site URL
            username: SharePoint username (email)
            password: SharePoint password
        """
        self.site_url = site_url.rstrip('/')
        self.username = username
        self.password = password
        self.ctx = None
        self.temp_dir = tempfile.mkdtemp(prefix="sharepoint_")
        print(f"Created temporary directory: {self.temp_dir}")
        
        # SharePoint file size limit (250 MB)
        self.file_size_limit = 250 * 1024 * 1024
        
        # Connect to SharePoint
        self._connect()
        
        # Get the web object
        self.web = self.ctx.web
        self.ctx.load(self.web)
        self.ctx.execute_query()
        
        # Extract site information
        self.site_title = self.web.properties['Title']
        self.site_server_relative_url = self.web.properties['ServerRelativeUrl']
        print(f"Connected to SharePoint site: {self.site_title}")
        print(f"Site server relative URL: {self.site_server_relative_url}")
        
        # Parse the site URL to get the domain
        parsed_url = urlparse(site_url)
        self.domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
    
    def _connect(self):
        """Connect to SharePoint using the provided credentials."""
        try:
            user_credentials = UserCredential(self.username, self.password)
            self.ctx = ClientContext(self.site_url).with_credentials(user_credentials)
        except Exception as e:
            raise Exception(f"Error connecting to SharePoint: {str(e)}")
    
    def _normalize_path(self, path: str) -> str:
        """
        Normalize a path by removing leading/trailing slashes and converting backslashes.
        
        Args:
            path: Path to normalize
            
        Returns:
            Normalized path
        """
        # Replace backslashes with forward slashes
        path = path.replace('\\', '/')
        
        # Remove leading and trailing slashes
        path = path.strip('/')
        
        # Replace consecutive slashes with a single slash
        while '//' in path:
            path = path.replace('//', '/')
        
        return path
    
    def _get_server_relative_url(self, path: str) -> str:
        """
        Convert a path to a server-relative URL.
        
        Args:
            path: Path in SharePoint (e.g., "Shared Documents/folder/file.txt")
            
        Returns:
            Server-relative URL
        """
        # Normalize the path
        path = self._normalize_path(path)
        
        # Handle special characters in path
        # Don't encode slash since it's used as a path separator
        def encode_path_segment(segment):
            # Characters that need special handling in SharePoint URLs
            # Don't replace slashes as they are path separators
            special_chars = {
                '#': '%23',
                '%': '%25',
                '&': '%26',
                '+': '%2B',
                ';': '%3B',
                '=': '%3D',
                '?': '%3F',
                '@': '%40'
            }
            
            # Replace special characters
            for char, replacement in special_chars.items():
                segment = segment.replace(char, replacement)
            
            return segment
        
        # Apply encoding for each path segment
        encoded_path = '/'.join(encode_path_segment(segment) for segment in path.split('/'))
        
        # If path already starts with the site server relative URL, just ensure it has a leading slash
        if encoded_path.startswith(self._normalize_path(self.site_server_relative_url)):
            return f"/{encoded_path}"
        
        # If path starts with "Shared Documents", prepend the site server relative URL
        if encoded_path.startswith("Shared Documents"):
            # Remove "Shared Documents" from the path
            path_without_shared = encoded_path[len("Shared Documents"):].strip('/')
            # Construct the full server-relative URL
            return f"{self.site_server_relative_url}/Shared Documents/{path_without_shared}"
        
        # Otherwise, assume it's a path relative to the site
        return f"{self.site_server_relative_url}/{encoded_path}"
    
    def _split_path(self, path: str) -> Tuple[str, str]:
        """
        Split a path into folder path and file name.
        
        Args:
            path: Path to split
            
        Returns:
            Tuple of (folder_path, file_name)
        """
        path = self._normalize_path(path)
        parts = path.split('/')
        
        if len(parts) == 1:
            # Just a file name, no folder
            return "", parts[0]
        
        # Last part is the file name, the rest is the folder path
        return '/'.join(parts[:-1]), parts[-1]
    
    def file_exists(self, file_path: str) -> bool:
        """
        Check if a file exists in SharePoint.
        
        Args:
            file_path: Path to the file in SharePoint
            
        Returns:
            bool: True if the file exists, False otherwise
        """
        try:
            server_relative_url = self._get_server_relative_url(file_path)
            file = self.web.get_file_by_server_relative_url(server_relative_url)
            self.ctx.load(file)
            self.ctx.execute_query()
            return True
        except Exception as e:
            return False
    
    def folder_exists(self, folder_path: str) -> bool:
        """
        Check if a folder exists in SharePoint.
        
        Args:
            folder_path: Path to the folder in SharePoint
            
        Returns:
            bool: True if the folder exists, False otherwise
        """
        try:
            server_relative_url = self._get_server_relative_url(folder_path)
            folder = self.web.get_folder_by_server_relative_url(server_relative_url)
            self.ctx.load(folder)
            self.ctx.execute_query()
            return True
        except Exception as e:
            return False
    
    def ensure_folder_exists(self, folder_path: str) -> bool:
        """
        Ensure that a folder exists in SharePoint, creating it if necessary.
        
        Args:
            folder_path: Path to the folder in SharePoint
            
        Returns:
            bool: True if the folder exists or was created, False otherwise
        """
        # Normalize the path
        folder_path = self._normalize_path(folder_path)
        
        # Check if folder already exists
        if self.folder_exists(folder_path):
            return True
        
        # Split the path into parts
        parts = folder_path.split('/')
        
        # Start with an empty path
        current_path = ""
        
        # Create each folder in the path
        for i, part in enumerate(parts):
            if not part:
                continue
            
            # Update the current path
            if current_path:
                current_path = f"{current_path}/{part}"
            else:
                current_path = part
            
            # Check if this segment exists
            if not self.folder_exists(current_path):
                try:
                    # Get the parent folder
                    if i == 0:
                        # Special handling for the first level
                        parent_server_url = self.site_server_relative_url
                        parent_folder = self.web.get_folder_by_server_relative_url(parent_server_url)
                    else:
                        parent_path = '/'.join(parts[:i])
                        parent_server_url = self._get_server_relative_url(parent_path)
                        parent_folder = self.web.get_folder_by_server_relative_url(parent_server_url)
                    
                    # Create the folder - try multiple methods
                    self.ctx.load(parent_folder)
                    self.ctx.execute_query()
                    
                    # First attempt: direct creation
                    try:
                        parent_folder.folders.add(part).execute_query()
                        print(f"Created folder: {current_path}")
                    except Exception as e1:
                        print(f"First folder creation attempt failed for '{part}': {str(e1)}")
                        
                        # Second attempt: ensure_folder method
                        try:
                            from office365.sharepoint.folders.folder import Folder
                            server_relative_path = f"{parent_server_url}/{part}"
                            Folder.ensure_folder(self.ctx, self.web, server_relative_path)
                            print(f"Created folder (alternative method): {current_path}")
                        except Exception as e2:
                            print(f"Second folder creation attempt failed for '{part}': {str(e2)}")
                            return False
                    
                    # Wait a moment to ensure SharePoint has processed the folder creation
                    time.sleep(1.0)
                except Exception as e:
                    print(f"Error creating folder {current_path}: {str(e)}")
                    return False
        
        return True
    
    def download_file(self, sharepoint_path: str, local_path: Optional[str] = None) -> str:
        """
        Download a file from SharePoint to a local path.
        
        Args:
            sharepoint_path: Path to the file in SharePoint
            local_path: Local path to save the file to (optional)
            
        Returns:
            str: Path to the downloaded file
        """
        if not local_path:
            # Create a temporary file
            file_name = os.path.basename(sharepoint_path)
            local_path = os.path.join(self.temp_dir, file_name)
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        try:
            # Get the file from SharePoint
            server_relative_url = self._get_server_relative_url(sharepoint_path)
            file = self.web.get_file_by_server_relative_url(server_relative_url)
            
            # Download the file content
            with open(local_path, "wb") as local_file:
                file.download(local_file).execute_query()
            
            print(f"Downloaded: {sharepoint_path} -> {local_path}")
            return local_path
        except Exception as e:
            print(f"Error downloading file {sharepoint_path}: {str(e)}")
            raise
    
    def upload_file(self, local_path: str, sharepoint_path: str) -> bool:
        """
        Upload a file to SharePoint.
        
        Args:
            local_path: Local path to the file
            sharepoint_path: Path to save the file in SharePoint
            
        Returns:
            bool: True if the upload was successful, False otherwise
        """
        try:
            # Split the path into folder path and file name
            folder_path, file_name = self._split_path(sharepoint_path)
            
            # Ensure the folder exists
            if not self.ensure_folder_exists(folder_path):
                print(f"Failed to create folder: {folder_path}")
                return False
            
            # Get file size
            file_size = os.path.getsize(local_path)
            
            # Use chunked upload for large files
            if file_size > self.file_size_limit:
                return self._upload_large_file(local_path, folder_path, file_name)
            
            # Get the folder object
            server_relative_folder = self._get_server_relative_url(folder_path)
            folder = self.web.get_folder_by_server_relative_url(server_relative_folder)
            
            # Upload the file using direct binary upload
            try:
                with open(local_path, "rb") as content:
                    file_content = content.read()
                    folder.upload_file(file_name, file_content).execute_query()
            except Exception as upload_error:
                # If direct upload fails, try an alternative approach
                print(f"Direct upload failed, trying alternative method: {str(upload_error)}")
                
                # Try using the upload_file_content method instead
                with open(local_path, "rb") as content:
                    file_content = content.read()
                    from office365.sharepoint.files.file import File
                    
                    # Create the server relative URL for the file
                    file_url = f"{server_relative_folder}/{file_name}"
                    
                    # Use the create_file method which can handle some special characters better
                    File.save_binary(self.ctx, file_url, file_content)
            
            print(f"Uploaded: {local_path} -> {sharepoint_path}")
            return True
        except Exception as e:
            print(f"Error uploading file {local_path} to {sharepoint_path}: {str(e)}")
            return False
    
    def _upload_large_file(self, local_path: str, folder_path: str, file_name: str) -> bool:
        """
        Upload a large file to SharePoint using chunked upload.
        
        Args:
            local_path: Local path to the file
            folder_path: SharePoint folder path
            file_name: Name of the file
            
        Returns:
            bool: True if the upload was successful, False otherwise
        """
        try:
            # Get the folder
            server_relative_folder = self._get_server_relative_url(folder_path)
            folder = self.web.get_folder_by_server_relative_url(server_relative_folder)
            
            # Create a session for chunked upload
            try:
                with open(local_path, 'rb') as file_content:
                    uploaded_file = folder.files.create_upload_session(
                        source_path=local_path,
                        file_name=file_name
                    ).execute_query()
            except Exception as upload_error:
                print(f"Chunked upload failed, trying alternative method: {str(upload_error)}")
                
                # Try using the File.save_binary method for large files
                with open(local_path, "rb") as content:
                    file_content = content.read()
                    from office365.sharepoint.files.file import File
                    
                    # Create the server relative URL for the file
                    file_url = f"{server_relative_folder}/{file_name}"
                    
                    # Use the create_file method which can handle some special characters better
                    File.save_binary(self.ctx, file_url, file_content)
            
            print(f"Uploaded (chunked): {local_path} -> {folder_path}/{file_name}")
            return True
        except Exception as e:
            print(f"Error uploading large file {local_path}: {str(e)}")
            return False
    
    def copy_file_using_local_transfer(self, source_path: str, dest_path: str) -> bool:
        """
        Copy a file within SharePoint using a reliable download-upload method.
        
        Args:
            source_path: Source path in SharePoint
            dest_path: Destination path in SharePoint
            
        Returns:
            bool: True if the copy was successful, False otherwise
        """
        try:
            # Get the folder path and file name from the destination path
            dest_folder_path, dest_file_name = self._split_path(dest_path)
            
            # Preserve the original file extension
            base_name, extension = os.path.splitext(dest_file_name)
            
            # Check for patterns that might cause issues (only in the base name, not extension)
            has_special_patterns = '.' in base_name or any(c in base_name for c in "+=&?@#%")
            
            # If we have potential problematic characters, create a safe version for upload
            # but preserve the file extension
            if has_special_patterns:
                # Create a safer version of only the base filename for SharePoint
                safe_base_name = base_name.replace('.', '_')
                safe_file_name = safe_base_name + extension
                safe_dest_path = f"{dest_folder_path}/{safe_file_name}"
                print(f"Using safer filename but preserving extension: {safe_file_name}")
            else:
                safe_file_name = dest_file_name
                safe_dest_path = dest_path
            
            # Ensure destination folder exists with multiple retries
            for attempt in range(3):
                if self.ensure_folder_exists(dest_folder_path):
                    break
                print(f"Retry {attempt+1}/3 creating folder: {dest_folder_path}")
                time.sleep(1.0)
            else:
                print(f"Error copying {source_path}: destination folder {dest_folder_path} could not be created after 3 attempts")
                return False
            
            # Download the file to a temporary location
            try:
                temp_file = self.download_file(source_path)
                print(f"Successfully downloaded: {source_path} -> {temp_file}")
            except Exception as download_error:
                print(f"Error copying {source_path}: download failed: {str(download_error)}")
                return False
            
            # First try direct upload with original file name
            upload_success = False
            try:
                server_relative_folder = self._get_server_relative_url(dest_folder_path)
                folder = self.web.get_folder_by_server_relative_url(server_relative_folder)
                
                with open(temp_file, "rb") as content:
                    file_content = content.read()
                    folder.upload_file(dest_file_name, file_content).execute_query()
                
                upload_success = True
                print(f"Successfully uploaded with original filename: {temp_file} -> {dest_path}")
            except Exception as e:
                print(f"Upload with original filename failed: {str(e)}")
                
                # If original filename upload failed and we have a different safer name, try that
                if has_special_patterns:
                    try:
                        # Get the folder object
                        server_relative_folder = self._get_server_relative_url(dest_folder_path)
                        folder = self.web.get_folder_by_server_relative_url(server_relative_folder)
                        
                        # Upload the file using direct binary upload
                        with open(temp_file, "rb") as content:
                            file_content = content.read()
                            folder.upload_file(safe_file_name, file_content).execute_query()
                        
                        upload_success = True
                        print(f"Successfully uploaded with safer filename: {temp_file} -> {dest_folder_path}/{safe_file_name}")
                        
                        # If the safer name is different from the original, try to rename it back
                        try:
                            if safe_file_name != dest_file_name:
                                print(f"Attempting to rename {safe_file_name} back to {dest_file_name}...")
                                safe_file_url = f"{server_relative_folder}/{safe_file_name}"
                                dest_file_url = f"{server_relative_folder}/{dest_file_name}"
                                
                                safer_file = self.web.get_file_by_server_relative_url(safe_file_url)
                                safer_file.moveto(dest_file_url, 1).execute_query()
                                print(f"Successfully renamed to original filename")
                        except Exception as rename_error:
                            print(f"Note: Uploaded with safer filename, but could not rename to original: {str(rename_error)}")
                    except Exception as alt_e:
                        print(f"Upload with safer filename also failed: {str(alt_e)}")
                        
                        # Try alternative upload method
                        try:
                            from office365.sharepoint.files.file import File
                            file_url = f"{server_relative_folder}/{safe_file_name}"
                            with open(temp_file, "rb") as content:
                                file_content = content.read()
                                File.save_binary(self.ctx, file_url, file_content)
                            
                            upload_success = True
                            print(f"Successfully uploaded (final alternative method): {temp_file} -> {dest_folder_path}/{safe_file_name}")
                        except Exception as final_e:
                            print(f"All upload methods failed: {str(final_e)}")
            
            # If upload was successful
            if upload_success:
                print(f"Copied: {source_path} -> {dest_path}")
                return True
            else:
                print(f"Failed to copy: {source_path} -> {dest_path}")
                return False
        except Exception as e:
            print(f"Error copying file {source_path} to {dest_path}: {str(e)}")
            return False
    
    def copy_file(self, source_path: str, dest_path: str) -> bool:
        """
        Copy a file within SharePoint using direct API call.
        
        Args:
            source_path: Source path in SharePoint
            dest_path: Destination path in SharePoint
            
        Returns:
            bool: True if the copy was successful, False otherwise
        """
        # Use the local transfer method which is more reliable
        return self.copy_file_using_local_transfer(source_path, dest_path)
    
    def list_files(self, folder_path: str) -> List[str]:
        """
        List files in a SharePoint folder.
        
        Args:
            folder_path: Path to the folder in SharePoint
            
        Returns:
            List of file names
        """
        try:
            server_relative_url = self._get_server_relative_url(folder_path)
            folder = self.web.get_folder_by_server_relative_url(server_relative_url)
            files = folder.files
            self.ctx.load(files)
            self.ctx.execute_query()
            
            return [file.properties["Name"] for file in files]
        except Exception as e:
            print(f"Error listing files in {folder_path}: {str(e)}")
            return []
    
    def list_folders(self, folder_path: str) -> List[str]:
        """
        List subfolders in a SharePoint folder.
        
        Args:
            folder_path: Path to the folder in SharePoint
            
        Returns:
            List of folder names
        """
        try:
            server_relative_url = self._get_server_relative_url(folder_path)
            folder = self.web.get_folder_by_server_relative_url(server_relative_url)
            folders = folder.folders
            self.ctx.load(folders)
            self.ctx.execute_query()
            
            return [folder.properties["Name"] for folder in folders]
        except Exception as e:
            print(f"Error listing folders in {folder_path}: {str(e)}")
            return []
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file in SharePoint.
        
        Args:
            file_path: Path to the file in SharePoint
            
        Returns:
            bool: True if the file was deleted successfully, False otherwise
        """
        try:
            server_relative_url = self._get_server_relative_url(file_path)
            file_to_delete = self.web.get_file_by_server_relative_url(server_relative_url)
            file_to_delete.delete_object()
            self.ctx.execute_query()
            print(f"Deleted SharePoint file: {file_path}") # Optional: for verbose output
            return True
        except Exception as e:
            # Handle case where file doesn't exist gracefully?
            # Current Office365 client might raise Exception if file not found before delete
            if "File Not Found" in str(e) or "404 Client Error" in str(e):
                 print(f"Warning: File not found, cannot delete: {file_path}")
                 return False # Or True if not finding it is acceptable?
            
            print(f"Error deleting file {file_path}: {str(e)}")
            return False
    
    def cleanup(self):
        """Clean up temporary files."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            print(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            print(f"Error cleaning up temporary directory: {str(e)}")
